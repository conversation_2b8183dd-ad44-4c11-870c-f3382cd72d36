{"backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_create_project_success": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_create_project_validation_error": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_create_project_duplicate_error": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_get_project_by_id_success": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_get_project_by_code_success": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_get_project_not_found": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_update_project_success": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_update_project_not_found": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_delete_project_success": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_delete_project_not_found": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_list_projects_success": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_list_projects_with_pagination": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_list_projects_include_deleted": true, "backend/tests/test_api/test_project_routes.py::TestProjectRoutes::test_invalid_pagination_parameters": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_create_project": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_id_existing": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_id_nonexistent": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_code_existing": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_code_nonexistent": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_name_existing": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_by_name_nonexistent": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_all_projects": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_all_with_pagination": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_active_projects": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_active_projects_with_pagination": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_update_project": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_update_nonexistent_project": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_soft_delete_project": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_soft_delete_nonexistent_project": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_soft_delete_already_deleted_project": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_by_name": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_by_project_number": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_by_description": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_case_insensitive": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_excludes_deleted": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_search_projects_with_pagination": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_count_active_projects": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_count_active_projects_empty_db": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_project_with_related_data": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_get_project_with_related_data_nonexistent": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_unique_constraint_name": true, "backend/tests/test_repositories/test_project_repository.py::TestProjectRepository::test_unique_constraint_project_number": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_create_project_success": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_create_project_validation_error": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_create_project_duplicate_name_error": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_get_project_details_by_id": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_get_project_details_by_code": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_get_project_details_not_found": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_update_project_success": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_delete_project_success": true, "backend/tests/test_services/test_project_service.py::TestProjectService::test_get_projects_list_success": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_valid_component_creation": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_minimal_component_creation": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_name_validation_empty": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_name_validation_whitespace": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_name_normalization": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_category_id_validation": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_specific_data_json_validation_valid": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_specific_data_json_validation_invalid": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_specific_data_empty_string": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCreateSchema::test_specific_data_whitespace_only": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentUpdateSchema::test_valid_partial_update": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentUpdateSchema::test_empty_update": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentUpdateSchema::test_update_validation_same_as_create": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCategoryCreateSchema::test_valid_category_creation": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCategoryCreateSchema::test_minimal_category_creation": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCategoryCreateSchema::test_category_with_parent": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCategoryCreateSchema::test_category_name_validation_empty": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCategoryCreateSchema::test_category_name_validation_whitespace": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCategoryCreateSchema::test_category_name_normalization": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCategoryUpdateSchema::test_valid_partial_category_update": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCategoryUpdateSchema::test_empty_category_update": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentCategoryUpdateSchema::test_category_update_validation_same_as_create": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentReadSchema::test_read_schema_includes_all_fields": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentListResponseSchema::test_list_response_structure": true, "backend/tests/test_schemas/test_component_schemas.py::TestComponentListResponseSchema::test_empty_list_response": true, "backend/tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_create_pipe_success": true, "backend/tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_create_pipe_validation_error": true, "backend/tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_create_pipe_duplicate_error": true, "backend/tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_get_pipe_success": true, "backend/tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_get_pipe_not_found": true, "backend/tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_update_pipe_success": true, "backend/tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_delete_pipe_success": true, "backend/tests/test_api/test_heat_tracing_routes.py::TestPipeEndpoints::test_list_pipes_success": true, "backend/tests/test_api/test_heat_tracing_routes.py::TestVesselEndpoints::test_create_vessel_success": true, "backend/tests/test_api/test_heat_tracing_routes.py::TestVesselEndpoints::test_get_vessel_success": true, "backend/tests/test_api/test_heat_tracing_routes.py::TestCalculationEndpoints::test_calculate_pipe_heat_loss_success": true, "backend/tests/test_api/test_heat_tracing_routes.py::TestCalculationEndpoints::test_validate_standards_compliance_success": true, "backend/tests/test_api/test_heat_tracing_routes.py::TestDesignWorkflowEndpoints::test_execute_design_workflow_success": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestHeatLossCalculationSchemas::test_heat_loss_calculation_input_valid": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestHeatLossCalculationSchemas::test_heat_loss_calculation_input_fluid_temp_validation": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestHeatLossCalculationSchemas::test_heat_loss_calculation_result_valid": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestPipeSchemas::test_pipe_create_schema_valid": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestPipeSchemas::test_pipe_create_schema_name_validation": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestPipeSchemas::test_pipe_create_schema_diameter_validation": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestPipeSchemas::test_pipe_update_schema_partial": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestPipeSchemas::test_pipe_read_schema_from_dict": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestVesselSchemas::test_vessel_create_schema_valid": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestVesselSchemas::test_vessel_create_schema_dimensions_validation": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestVesselSchemas::test_vessel_create_schema_dimensions_type_validation": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestHTCircuitSchemas::test_htcircuit_create_schema_valid_pipe": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestHTCircuitSchemas::test_htcircuit_create_schema_valid_vessel": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestHTCircuitSchemas::test_htcircuit_create_schema_pipe_or_vessel_validation": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestHTCircuitSchemas::test_htcircuit_create_schema_both_pipe_and_vessel_validation": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestControlCircuitSchemas::test_control_circuit_create_schema_valid": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestControlCircuitSchemas::test_control_circuit_limiting_setpoint_validation": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestDesignWorkflowSchemas::test_design_input_schema_valid": true, "backend/tests/test_schemas/test_heat_tracing_schemas.py::TestDesignWorkflowSchemas::test_design_input_schema_pipe_or_vessel_validation": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_create_pipe_success": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_create_pipe_validation_error": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_create_pipe_duplicate_error": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_get_pipe_details_success": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_get_pipe_details_not_found": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_get_pipe_details_deleted": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_update_pipe_success": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_update_pipe_not_found": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_delete_pipe_success": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServicePipeOperations::test_get_pipes_list_success": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceVesselOperations::test_create_vessel_success": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceVesselOperations::test_create_vessel_invalid_dimensions": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceVesselOperations::test_get_vessel_details_success": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceCalculations::test_calculate_pipe_heat_loss_success": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceCalculations::test_calculate_pipe_heat_loss_pipe_not_found": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceCalculations::test_validate_standards_compliance_success": true, "backend/tests/test_services/test_heat_tracing_service.py::TestHeatTracingServiceDesignWorkflow::test_execute_design_workflow_success": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_create_pipe_success": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_get_pipe_by_id_success": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_get_pipe_by_id_not_found": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_get_pipes_by_project_id": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_get_pipe_by_line_tag": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_get_pipe_by_line_tag_not_found": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_get_pipes_without_circuits": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_update_heat_loss_calculation": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_update_heat_loss_calculation_not_found": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_count_by_project": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestPipeRepository::test_get_with_heat_loss_calculations": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestVesselRepository::test_create_vessel_success": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestVesselRepository::test_get_vessel_by_equipment_tag": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestVesselRepository::test_get_vessels_without_circuits": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestVesselRepository::test_update_heat_loss_calculation": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestHTCircuitRepository::test_create_htcircuit_success": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestHTCircuitRepository::test_get_by_feeder_id": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestHTCircuitRepository::test_get_by_pipe_id": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestHTCircuitRepository::test_update_load_calculation": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestHTCircuitRepository::test_get_total_feeder_load": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestControlCircuitRepository::test_create_control_circuit_success": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestControlCircuitRepository::test_get_by_switchboard_id": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestControlCircuitRepository::test_get_with_limiting_function": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestHeatTracingRepository::test_initialization": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestHeatTracingRepository::test_get_project_summary_empty": true, "backend/tests/test_repositories/test_heat_tracing_repository.py::TestHeatTracingRepository::test_get_design_readiness_empty": true, "backend/tests/test_switchboard_api.py": true, "backend/tests/test_user_api.py": true, "backend/tests/test_user_service.py": true, "backend/tests/test_switchboard_service.py::TestSwitchboardService::test_create_switchboard_success": true, "backend/tests/test_switchboard_service.py::TestSwitchboardService::test_create_switchboard_invalid_voltage": true, "backend/tests/test_switchboard_service.py::TestSwitchboardService::test_get_switchboard_success": true, "backend/tests/test_switchboard_service.py::TestSwitchboardService::test_get_switchboard_not_found": true, "backend/tests/test_switchboard_service.py::TestSwitchboardService::test_update_switchboard_success": true, "backend/tests/test_switchboard_service.py::TestSwitchboardService::test_update_switchboard_invalid_voltage": true, "backend/tests/test_switchboard_service.py::TestSwitchboardService::test_delete_switchboard_success": true, "backend/tests/test_switchboard_service.py::TestSwitchboardService::test_delete_switchboard_not_found": true, "backend/tests/test_switchboard_service.py::TestSwitchboardService::test_create_feeder_success": true, "backend/tests/test_switchboard_service.py::TestSwitchboardService::test_create_feeder_switchboard_not_found": true, "backend/tests/test_switchboard_service.py::TestSwitchboardService::test_add_switchboard_component_success": true, "backend/tests/test_switchboard_service.py::TestSwitchboardService::test_add_switchboard_component_switchboard_not_found": true, "backend/tests/test_switchboard_service.py::TestSwitchboardService::test_add_switchboard_component_component_not_found": true, "backend/tests/test_switchboard_service.py::TestSwitchboardService::test_get_switchboard_load_summary_success": true, "backend/tests/test_switchboard_service.py::TestSwitchboardService::test_get_switchboard_capacity_analysis_success": true, "backend/tests/test_electrical_service.py::TestElectricalService::test_cable_sizing_calculation_success": true, "backend/tests/test_electrical_service.py::TestElectricalService::test_voltage_drop_calculation_success": true, "backend/tests/test_electrical_service.py::TestElectricalService::test_electrical_standards_validation_success": true, "backend/tests/test_electrical_service.py::TestElectricalService::test_calculate_load_for_electrical_node_success": true, "backend/tests/test_electrical_service.py::TestElectricalService::test_calculate_load_for_nonexistent_node": true, "backend/tests/test_electrical_service.py::TestElectricalService::test_cable_sizing_calculation_error_handling": true, "backend/tests/test_electrical_service.py::TestElectricalService::test_voltage_drop_calculation_error_handling": true, "backend/tests/test_electrical_service.py::TestElectricalService::test_generate_recommendations": true, "backend/tests/test_minimal_integration.py::TestMinimalIntegration::test_switchboard_schema_with_enum": true, "backend/tests/test_minimal_integration.py::TestMinimalIntegration::test_user_schema_validation": true, "backend/tests/test_minimal_integration.py::TestMinimalIntegration::test_schema_validation_errors": true, "backend/tests/test_minimal_integration.py::TestMinimalIntegration::test_enum_values": true, "backend/tests/test_minimal_integration.py::TestMinimalIntegration::test_schema_serialization": true, "backend/tests/test_minimal_integration.py::TestMinimalIntegration::test_optional_fields": true, "backend/tests/test_minimal_integration.py::TestMinimalIntegration::test_default_values": true, "backend/tests/test_switchboard_schemas.py::TestSwitchboardSchemas::test_switchboard_create_schema_valid": true, "backend/tests/test_switchboard_schemas.py::TestSwitchboardSchemas::test_switchboard_create_schema_invalid_voltage": true, "backend/tests/test_switchboard_schemas.py::TestSwitchboardSchemas::test_switchboard_create_schema_invalid_phases": true, "backend/tests/test_switchboard_schemas.py::TestSwitchboardSchemas::test_switchboard_create_schema_empty_name": true, "backend/tests/test_switchboard_schemas.py::TestSwitchboardSchemas::test_switchboard_update_schema_partial": true, "backend/tests/test_switchboard_schemas.py::TestSwitchboardSchemas::test_feeder_create_schema_valid": true, "backend/tests/test_switchboard_schemas.py::TestSwitchboardSchemas::test_feeder_create_schema_empty_name": true, "backend/tests/test_switchboard_schemas.py::TestSwitchboardSchemas::test_switchboard_component_create_schema_valid": true, "backend/tests/test_switchboard_schemas.py::TestSwitchboardSchemas::test_switchboard_component_create_schema_invalid_quantity": true, "backend/tests/test_switchboard_schemas.py::TestSwitchboardSchemas::test_feeder_component_create_schema_valid": true, "backend/tests/test_switchboard_schemas.py::TestSwitchboardSchemas::test_switchboard_load_summary_schema_valid": true, "backend/tests/test_switchboard_schemas.py::TestSwitchboardSchemas::test_switchboard_capacity_analysis_schema_valid": true, "backend/tests/test_switchboard_schemas.py::TestSwitchboardSchemas::test_switchboard_capacity_analysis_schema_overloaded": true, "backend/tests/test_switchboard_schemas.py::TestSwitchboardSchemaEdgeCases::test_switchboard_minimum_voltage": true, "backend/tests/test_switchboard_schemas.py::TestSwitchboardSchemaEdgeCases::test_switchboard_maximum_voltage": true, "backend/tests/test_switchboard_schemas.py::TestSwitchboardSchemaEdgeCases::test_component_maximum_quantity": true, "backend/tests/test_switchboard_schemas.py::TestSwitchboardSchemaEdgeCases::test_position_whitespace_handling": true, "backend/tests/test_switchboard_schemas.py::TestSwitchboardSchemaEdgeCases::test_optional_fields_none": true, "backend/tests/test_user_schemas.py::TestUserSchemas::test_user_create_schema_valid": true, "backend/tests/test_user_schemas.py::TestUserSchemas::test_user_create_schema_password_validation": true, "backend/tests/test_user_schemas.py::TestUserSchemas::test_user_create_schema_password_no_uppercase": true, "backend/tests/test_user_schemas.py::TestUserSchemas::test_user_create_schema_password_no_digit": true, "backend/tests/test_user_schemas.py::TestUserSchemas::test_user_create_schema_empty_name": true, "backend/tests/test_user_schemas.py::TestUserSchemas::test_user_create_schema_invalid_email": true, "backend/tests/test_user_schemas.py::TestUserSchemas::test_user_update_schema_partial": true, "backend/tests/test_user_schemas.py::TestUserSchemas::test_login_request_schema_valid": true, "backend/tests/test_user_schemas.py::TestUserSchemas::test_login_request_schema_invalid_email": true, "backend/tests/test_user_schemas.py::TestUserSchemas::test_password_change_request_schema_valid": true, "backend/tests/test_user_schemas.py::TestUserSchemas::test_password_change_request_schema_weak_new_password": true, "backend/tests/test_user_schemas.py::TestUserSchemas::test_password_reset_confirm_schema_valid": true, "backend/tests/test_user_schemas.py::TestUserPreferenceSchemas::test_user_preference_create_schema_valid": true, "backend/tests/test_user_schemas.py::TestUserPreferenceSchemas::test_user_preference_create_schema_invalid_theme": true, "backend/tests/test_user_schemas.py::TestUserPreferenceSchemas::test_user_preference_create_schema_invalid_temperature_range": true, "backend/tests/test_user_schemas.py::TestUserPreferenceSchemas::test_user_preference_create_schema_temperature_boundaries": true, "backend/tests/test_user_schemas.py::TestUserPreferenceSchemas::test_user_preference_create_schema_invalid_safety_margin": true, "backend/tests/test_user_schemas.py::TestUserPreferenceSchemas::test_user_preference_update_schema_partial": true, "backend/tests/test_user_schemas.py::TestUserSchemaEdgeCases::test_user_name_whitespace_handling": true, "backend/tests/test_user_schemas.py::TestUserSchemaEdgeCases::test_user_preference_theme_case_sensitivity": true, "backend/tests/test_user_schemas.py::TestUserSchemaEdgeCases::test_user_preference_equal_temperatures": true, "backend/tests/test_user_schemas.py::TestUserSchemaEdgeCases::test_user_optional_email": true, "backend/tests/test_user_schemas.py::TestUserSchemaEdgeCases::test_password_minimum_length": true, "backend/tests/test_repositories/test_electrical_repository.py::TestElectricalNodeRepository::test_get_by_project_id_success": true, "backend/tests/test_repositories/test_electrical_repository.py::TestElectricalNodeRepository::test_get_by_project_id_database_error": true, "backend/tests/test_repositories/test_electrical_repository.py::TestElectricalNodeRepository::test_get_by_node_type_success": true, "backend/tests/test_repositories/test_electrical_repository.py::TestElectricalNodeRepository::test_get_nodes_with_capacity_success": true, "backend/tests/test_repositories/test_electrical_repository.py::TestElectricalNodeRepository::test_count_by_project_success": true, "backend/tests/test_repositories/test_electrical_repository.py::TestElectricalNodeRepository::test_count_by_project_none_result": true, "backend/tests/test_repositories/test_electrical_repository.py::TestCableRouteRepository::test_get_routes_by_node_both_directions": true, "backend/tests/test_repositories/test_electrical_repository.py::TestCableRouteRepository::test_get_routes_by_node_invalid_direction": true, "backend/tests/test_repositories/test_electrical_repository.py::TestCableRouteRepository::test_get_routes_by_installation_method_success": true, "backend/tests/test_repositories/test_electrical_repository.py::TestCableRouteRepository::test_get_routes_with_high_voltage_drop_success": true, "backend/tests/test_repositories/test_electrical_repository.py::TestLoadCalculationRepository::test_get_by_electrical_node_id_success": true, "backend/tests/test_repositories/test_electrical_repository.py::TestLoadCalculationRepository::test_get_by_load_type_success": true, "backend/tests/test_repositories/test_electrical_repository.py::TestLoadCalculationRepository::test_get_total_power_by_node_success": true, "backend/tests/test_repositories/test_electrical_repository.py::TestLoadCalculationRepository::test_get_total_power_by_node_none_result": true, "backend/tests/test_repositories/test_electrical_repository.py::TestVoltageDropCalculationRepository::test_get_by_cable_route_id_success": true, "backend/tests/test_repositories/test_electrical_repository.py::TestVoltageDropCalculationRepository::test_get_non_compliant_calculations_success": true, "backend/tests/test_repositories/test_electrical_repository.py::TestVoltageDropCalculationRepository::test_get_average_voltage_drop_by_project_success": true, "backend/tests/test_repositories/test_electrical_repository.py::TestVoltageDropCalculationRepository::test_count_by_compliance_status_success": true, "backend/tests/test_schemas/test_document_schemas.py::TestImportedDataRevisionSchemas::test_valid_imported_data_revision_creation": true, "backend/tests/test_schemas/test_document_schemas.py::TestImportedDataRevisionSchemas::test_filename_validation_empty": true, "backend/tests/test_schemas/test_document_schemas.py::TestImportedDataRevisionSchemas::test_filename_validation_invalid_extension": true, "backend/tests/test_schemas/test_document_schemas.py::TestImportedDataRevisionSchemas::test_filename_validation_valid_extensions": true, "backend/tests/test_schemas/test_document_schemas.py::TestImportedDataRevisionSchemas::test_revision_identifier_validation_invalid_chars": true, "backend/tests/test_schemas/test_document_schemas.py::TestImportedDataRevisionSchemas::test_revision_identifier_validation_valid_chars": true, "backend/tests/test_schemas/test_document_schemas.py::TestImportedDataRevisionSchemas::test_update_schema_partial_fields": true, "backend/tests/test_schemas/test_document_schemas.py::TestExportedDocumentSchemas::test_valid_exported_document_creation": true, "backend/tests/test_schemas/test_document_schemas.py::TestExportedDocumentSchemas::test_document_filename_validation_empty": true, "backend/tests/test_schemas/test_document_schemas.py::TestExportedDocumentSchemas::test_document_filename_validation_invalid_extension": true, "backend/tests/test_schemas/test_document_schemas.py::TestExportedDocumentSchemas::test_document_filename_validation_valid_extensions": true, "backend/tests/test_schemas/test_document_schemas.py::TestCalculationStandardSchemas::test_valid_calculation_standard_creation": true, "backend/tests/test_schemas/test_document_schemas.py::TestCalculationStandardSchemas::test_standard_name_validation_empty": true, "backend/tests/test_schemas/test_document_schemas.py::TestCalculationStandardSchemas::test_standard_code_validation_empty": true, "backend/tests/test_schemas/test_document_schemas.py::TestCalculationStandardSchemas::test_standard_code_normalization": true, "backend/tests/test_schemas/test_document_schemas.py::TestCalculationStandardSchemas::test_standard_code_validation_invalid_chars": true, "backend/tests/test_schemas/test_document_schemas.py::TestCalculationStandardSchemas::test_parameters_json_validation_invalid_json": true, "backend/tests/test_schemas/test_document_schemas.py::TestCalculationStandardSchemas::test_parameters_json_validation_not_object": true, "backend/tests/test_schemas/test_document_schemas.py::TestCalculationStandardSchemas::test_parameters_json_validation_valid_object": true, "backend/tests/test_schemas/test_document_schemas.py::TestEnums::test_import_type_enum_values": true, "backend/tests/test_schemas/test_document_schemas.py::TestEnums::test_document_type_enum_values": true, "backend/tests/test_schemas/test_document_schemas.py::TestEnums::test_file_format_enum_values": true, "backend/tests/test_schemas/test_electrical_schemas.py::TestCalculationIntegrationSchemas::test_cable_sizing_calculation_input_valid": true, "backend/tests/test_schemas/test_electrical_schemas.py::TestCalculationIntegrationSchemas::test_cable_sizing_calculation_input_invalid_power": true, "backend/tests/test_schemas/test_electrical_schemas.py::TestCalculationIntegrationSchemas::test_voltage_drop_calculation_input_valid": true, "backend/tests/test_schemas/test_electrical_schemas.py::TestCalculationIntegrationSchemas::test_cable_sizing_calculation_result_valid": true, "backend/tests/test_schemas/test_electrical_schemas.py::TestElectricalNodeSchemas::test_electrical_node_create_schema_valid": true, "backend/tests/test_schemas/test_electrical_schemas.py::TestElectricalNodeSchemas::test_electrical_node_create_schema_name_validation": true, "backend/tests/test_schemas/test_electrical_schemas.py::TestElectricalNodeSchemas::test_electrical_node_update_schema_partial": true, "backend/tests/test_schemas/test_electrical_schemas.py::TestElectricalNodeSchemas::test_electrical_node_read_schema_from_dict": true, "backend/tests/test_schemas/test_electrical_schemas.py::TestCableRouteSchemas::test_cable_route_create_schema_valid": true, "backend/tests/test_schemas/test_electrical_schemas.py::TestCableRouteSchemas::test_cable_route_create_schema_same_nodes_validation": true, "backend/tests/test_schemas/test_electrical_schemas.py::TestCableRouteSchemas::test_cable_route_temperature_range_validation": true, "backend/tests/test_schemas/test_electrical_schemas.py::TestLoadCalculationSchemas::test_load_calculation_create_schema_valid": true, "backend/tests/test_schemas/test_electrical_schemas.py::TestLoadCalculationSchemas::test_load_calculation_electrical_parameters_validation": true, "backend/tests/test_schemas/test_electrical_schemas.py::TestDesignWorkflowSchemas::test_electrical_design_input_schema_valid": true, "backend/tests/test_schemas/test_electrical_schemas.py::TestDesignWorkflowSchemas::test_electrical_design_input_schema_node_or_route_validation": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_valid_project_creation": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_project_number_normalization": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_name_validation_empty": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_name_validation_whitespace": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_project_number_validation_empty": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_project_number_validation_invalid_chars": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_temperature_range_validation": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_temperature_bounds_validation": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_wind_speed_validation": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_voltages_json_validation_valid": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_voltages_json_validation_invalid_json": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_voltages_json_validation_not_array": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectCreateSchema::test_voltages_json_validation_negative_voltage": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectUpdateSchema::test_valid_partial_update": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectUpdateSchema::test_empty_update": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectUpdateSchema::test_update_validation_same_as_create": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectReadSchema::test_from_orm_conversion": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectReadSchema::test_read_schema_includes_all_fields": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectSummarySchema::test_summary_includes_essential_fields": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectSummarySchema::test_summary_excludes_detailed_fields": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectListResponseSchema::test_list_response_structure": true, "backend/tests/test_schemas/test_project_schemas.py::TestProjectListResponseSchema::test_empty_list_response": true, "backend/tests/test_services/test_component_service.py::TestComponentService::test_create_component_success": true, "backend/tests/test_services/test_component_service.py::TestComponentService::test_create_component_category_not_found": true, "backend/tests/test_services/test_component_service.py::TestComponentService::test_create_component_duplicate_name": true, "backend/tests/test_services/test_component_service.py::TestComponentService::test_create_component_integrity_error": true, "backend/tests/test_services/test_component_service.py::TestComponentService::test_get_component_details_success": true, "backend/tests/test_services/test_component_service.py::TestComponentService::test_get_component_details_not_found": true, "backend/tests/test_services/test_component_service.py::TestComponentService::test_get_component_details_deleted": true, "backend/tests/test_services/test_component_service.py::TestComponentService::test_update_component_success": true, "backend/tests/test_services/test_component_service.py::TestComponentService::test_update_component_not_found": true, "backend/tests/test_services/test_component_service.py::TestComponentService::test_delete_component_success": true, "backend/tests/test_services/test_component_service.py::TestComponentService::test_delete_component_not_found": true, "backend/tests/test_services/test_component_service.py::TestComponentService::test_get_components_list_success": true, "backend/tests/test_services/test_component_service.py::TestComponentService::test_get_components_list_with_category_filter": true, "backend/tests/test_services/test_component_service.py::TestComponentService::test_get_components_list_with_search": true, "backend/tests/test_activity_log_repository.py::TestActivityLogRepository::test_repository_initialization": true, "backend/tests/test_activity_log_repository.py::TestActivityLogRepository::test_get_by_user_id": true, "backend/tests/test_activity_log_repository.py::TestActivityLogRepository::test_get_by_entity": true, "backend/tests/test_activity_log_repository.py::TestActivityLogRepository::test_get_by_event_type": true, "backend/tests/test_activity_log_repository.py::TestActivityLogRepository::test_get_by_date_range": true, "backend/tests/test_activity_log_repository.py::TestActivityLogRepository::test_search_by_details": true, "backend/tests/test_activity_log_repository.py::TestActivityLogRepository::test_filter_activity_logs": true, "backend/tests/test_activity_log_repository.py::TestActivityLogRepository::test_count_filtered_activity_logs": true, "backend/tests/test_activity_log_repository.py::TestActivityLogRepository::test_get_security_events": true, "backend/tests/test_activity_log_repository.py::TestActivityLogRepository::test_get_user_activity_summary": true, "backend/tests/test_activity_log_repository.py::TestActivityLogRepository::test_get_daily_activity_counts": true, "backend/tests/test_activity_log_repository.py::TestActivityLogRepository::test_get_recent_activity": true, "backend/tests/test_activity_log_repository.py::TestActivityLogRepository::test_delete_old_logs": true, "backend/tests/test_activity_log_repository.py::TestActivityLogRepository::test_get_unique_users_count": true, "backend/tests/test_activity_log_repository.py::TestActivityLogRepository::test_get_unique_entities_count": true, "backend/tests/test_activity_log_repository.py::TestActivityLogRepository::test_database_error_handling": true, "backend/tests/test_activity_log_repository.py::TestActivityLogRepository::test_empty_results": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_service_initialization": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_log_event_success": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_log_event_system_event": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_log_event_invalid_user": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_log_user_action_success": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_log_user_action_invalid_action": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_log_user_action_invalid_entity_type": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_log_authentication_event_success": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_log_authentication_event_failed_login": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_log_system_event_success": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_get_activity_log_success": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_get_activity_log_not_found": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_get_user_activity_logs_success": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_get_user_activity_logs_user_not_found": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_log_security_event_success": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_log_security_event_invalid_severity": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_log_security_event_invalid_threat_level": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_update_activity_log_success": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_update_activity_log_not_found": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_delete_old_activity_logs_success": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_delete_old_activity_logs_invalid_days": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_generate_audit_report_success": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_get_user_activity_summary_success": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_get_user_activity_summary_invalid_date_range": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_database_error_handling": true, "backend/tests/test_activity_log_service.py::TestActivityLogService::test_transaction_rollback_on_error": true, "backend/tests/test_api/test_switchboard_api.py": true, "backend/tests/test_api/test_user_api.py": true, "backend/tests/test_services/test_user_service.py": true}